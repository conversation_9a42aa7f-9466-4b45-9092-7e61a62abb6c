# from django.urls import path
# from .views import GraphBofListAPIView

# urlpatterns = [
#     path('graphbof/', GraphBofListAPIView.as_view(), name='graphbof-list'),
# ]



from django.urls import path
from .views import graph_bof_status_view, graph_bof_api, graph_lrf_status_view, graph_lrf_api

urlpatterns = [
    path('graphbof/status/', graph_bof_status_view, name='graphbof-status'),  # HTML View
    path('graphbof/api/', graph_bof_api, name='graphbof-api'),               # API View
    path('graphlrf/status/', graph_lrf_status_view, name='graphlrf-status'),  # HTML View
    path('graphlrf/api/', graph_lrf_api, name='graphlrf-api'),               # API View
]
