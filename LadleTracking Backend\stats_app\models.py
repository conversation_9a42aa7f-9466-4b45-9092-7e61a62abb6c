from django.db import models

class GraphBof(models.Model):
    sr_no = models.AutoField(primary_key=True, db_column='Sr_no')
    heat_id = models.CharField(max_length=50, db_column='Heat_id')
    start_date = models.DateTimeField(db_column='Start_date')
    stop_date = models.DateTimeField(db_column='Stop_date')
    steel_grade = models.CharField(max_length=50, db_column='Steel_grade')
    hot_metal_temperature = models.CharField(max_length=50, db_column='Hot_metal_temperature')
    hot_metal_weight = models.CharField(max_length=50, db_column='Hot_metal_weight')
    total_scrap_charge = models.CharField(max_length=50, db_column='Total_scrap_charge')
    total_oxygen_consumption = models.CharField(max_length=50, db_column='Total_oxygen_consumption')
    tap_to_tap_time = models.CharField(max_length=50, db_column='Tap_to_tap_time')
    blowing_duration = models.CharField(max_length=50, db_column='Blowing_duration')
    aim_carbon = models.CharField(max_length=50, db_column='Aim_carbon')
    actual_carbon = models.CharField(max_length=50, db_column='Actual_carbon')
    aim_temperature = models.CharField(max_length=50, db_column='Aim_temperature')
    actual_temperature = models.CharField(max_length=50, db_column='Actual_temperature')

    class Meta:
        db_table = 'GraphBof'


class GraphLrf(models.Model):
    sr_no = models.AutoField(primary_key=True, db_column='sr_no')
    heart_id = models.CharField(max_length=50, db_column='heart_id')
    start_date = models.CharField(max_length=50, db_column='start_date')
    stop_date = models.CharField(max_length=50, db_column='stop_date')
    steel_grade = models.CharField(max_length=50, db_column='steel_grade')
    total_power_consumption = models.CharField(max_length=50, db_column='total_power_consumption')
    process_time_min = models.IntegerField(db_column='process_time_min')
    first_temprature = models.CharField(max_length=50, db_column='first_temprature')
    final_temprature = models.CharField(max_length=50, db_column='final_temprature')
    si_mn = models.CharField(max_length=50, db_column='SiMn')
    hc_fe_si = models.CharField(max_length=50, db_column='HcFe_Si')
    hc_fe_mn = models.CharField(max_length=50, db_column='HcFeMn')
    lime = models.CharField(max_length=50, db_column='Lime')
    cpc = models.CharField(max_length=50, db_column='CPC')
    sequence_no = models.CharField(max_length=50, db_column='Sequence_no')

    class Meta:
        db_table = 'GraphLrf'



