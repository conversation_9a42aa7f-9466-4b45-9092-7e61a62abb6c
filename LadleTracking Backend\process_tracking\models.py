from django.db import models

class HeatProcess(models.Model):
    sr_no = models.AutoField(primary_key=True, db_column='sr_no')
    heat_id = models.CharField(max_length=10, db_column='heat_id')
    unit = models.CharField(max_length=20, db_column='unit')
    unit_no = models.CharField(max_length=20, db_column='unit_no')
    laddle_arrival_time = models.DateTimeField(null=True, blank=True, db_column='laddle_arrival_time')
    start_time = models.DateTimeField(null=True, blank=True, db_column='start_time')
    stop_time = models.DateTimeField(null=True, blank=True, db_column='stop_time')  # ✅ fixed typo
    current_status = models.CharField(max_length=50, db_column='current_status')

    def __str__(self):
        return f"{self.heat_id} - {self.unit}"

    class Meta:
        db_table = 'unit_tracking'
        managed = False  # ✅ correct for external table
