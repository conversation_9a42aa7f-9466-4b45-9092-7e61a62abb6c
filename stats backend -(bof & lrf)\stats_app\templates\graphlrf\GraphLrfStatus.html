<!-- stats_app/templates/graphlrf/GraphLrfStatus.html -->
<!DOCTYPE html>
<html>
<head>
    <title>Graph LRF Status</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
            font-family: Arial, sans-serif;
        }

        th, td {
            border: 1px solid #444;
            padding: 8px;
            text-align: center;
        }

        th {
            background-color: #333;
            color: white;
        }

        tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        h2 {
            text-align: center;
        }
    </style>
</head>
<body>
    <h2>Graph LRF Status</h2>
    <table>
        <thead>
            <tr>
                <th>Sr No</th>
                <th>Heart ID</th>
                <th>Start Date</th>
                <th>Stop Date</th>
                <th>Steel Grade</th>
                <th>Total Power Consumption</th>
                <th>Process Time (min)</th>
                <th>First Temperature</th>
                <th>Final Temperature</th>
                <th>SiMn</th>
                <th>HcFe Si</th>
                <th>HcFeMn</th>
                <th>Lime</th>
                <th>CPC</th>
                <th>Sequence No</th>
            </tr>
        </thead>
        <tbody>
            {% for row in graph_lrf_data %}
            <tr>
                <td>{{ row.sr_no }}</td>
                <td>{{ row.heart_id }}</td>
                <td>{{ row.start_date }}</td>
                <td>{{ row.stop_date }}</td>
                <td>{{ row.steel_grade }}</td>
                <td>{{ row.total_power_consumption }}</td>
                <td>{{ row.process_time_min }}</td>
                <td>{{ row.first_temprature }}</td>
                <td>{{ row.final_temprature }}</td>
                <td>{{ row.SiMn }}</td>
                <td>{{ row.HcFe_Si }}</td>
                <td>{{ row.HcFeMn }}</td>
                <td>{{ row.Lime }}</td>
                <td>{{ row.CPC }}</td>
                <td>{{ row.Sequence_no }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
