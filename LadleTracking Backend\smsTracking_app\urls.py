from django.urls import path
from . import views

urlpatterns = [
    # HTML Status Views
    path('car/status/', views.car_status_view, name='car-status'),
    path('bof/status/', views.bof_status, name='bof-status'),
    path('sms/status/', views.sms_home, name='sms-status'),
    path('heat-process/status/', views.heat_process_status_view, name='heat-process-status'),
    path('graph-bof/status/', views.graph_bof_status_view, name='graph-bof-status'),
    path('graph-lrf/status/', views.graph_lrf_status_view, name='graph-lrf-status'),
    
    # Unified API endpoints under ladle-tracking
    path('api/ladle-tracking/car/', views.car_api, name='ladle-tracking-car-api'),
    path('api/ladle-tracking/bof/', views.bof_api, name='ladle-tracking-bof-api'),
    path('api/ladle-tracking/sms/', views.smsdb_api, name='ladle-tracking-sms-api'),
    path('api/ladle-tracking/heat-process/', views.heat_process_api, name='ladle-tracking-heat-process-api'),
    path('api/ladle-tracking/graph-bof/', views.graph_bof_api, name='ladle-tracking-graph-bof-api'),
    path('api/ladle-tracking/graph-lrf/', views.graph_lrf_api, name='ladle-tracking-graph-lrf-api'),
    
    # Backward compatibility
    path('main/', views.bof_status, name='api_root'),
    path('home/', views.sms_home, name='home'),
]

