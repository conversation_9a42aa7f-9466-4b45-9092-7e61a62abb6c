<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Heat Process Table</title>
    <style>
        body { 
            font-family: Arial, sans-serif;
            padding: 40px;
            background-color: #f9f9f9;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        table {
            width: 90%;
            margin: 0 auto;
            border-collapse: collapse;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        th, td {
            border: 1px solid #ccc;
            padding: 12px;
            text-align: center;
        }
        th {
            background-color: grey;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>

    <h1>Heat Process Table</h1>

    <table>
        <thead>
            <tr>
                <th>Sr_no</th>
                <th>Heat_ID</th>
                <th>Unit</th>
                <th>Unit_No</th>
                <th>Laddle_Arrival_Time</th>
                <th>Start_Time</th>
                <th>Stop_Time</th>
                <th>Current_Status</th>
            </tr>
        </thead>
        <tbody>
            {% for heat in processes %}
            <tr>
                <td>{{ heat.sr_no }}</td>
                <td>{{ heat.heat_id }}</td>
                <td>{{ heat.unit }}</td>
                <td>{{ heat.unit_no }}</td>
                <td>{{ heat.laddle_arrival_time|default:"-" }}</td>
                <td>{{ heat.start_time|default:"-" }}</td>
                <td>{{ heat.stop_time|default:"-" }}</td>
                <td>{{ heat.current_status }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

</body>
</html> 

