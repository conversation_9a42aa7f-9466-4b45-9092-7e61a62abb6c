from django.shortcuts import render
from django.db import connection
from rest_framework.decorators import api_view
from rest_framework.response import Response
from .models import GraphBof, GraphLrf
from .serializers import GraphBofSerializer, GraphLrfSerializer

# View to render HTML page using raw SQL and cursor
def graph_bof_status_view(request):
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT Sr_no, Heat_id, Start_date, Stop_date, Steel_grade,
                   Hot_metal_temperature, Hot_metal_weight, Total_scrap_charge,
                   Total_oxygen_consumption, Tap_to_tap_time, Blowing_duration,
                   Aim_carbon, Actual_carbon, Aim_temperature, Actual_temperature
            FROM Graph.dbo.GraphBof
        """)
        rows = cursor.fetchall()

    graph_bof_data = [
        {
            'Sr_no': row[0],
            'Heat_id': row[1],
            'Start_date': row[2],
            'Stop_date': row[3],
            'Steel_grade': row[4],
            'Hot_metal_temperature': row[5],
            'Hot_metal_weight': row[6],
            'Total_scrap_charge': row[7],
            'Total_oxygen_consumption': row[8],
            'Tap_to_tap_time': row[9],
            'Blowing_duration': row[10],
            'Aim_carbon': row[11],
            'Actual_carbon': row[12],
            'Aim_temperature': row[13],
            'Actual_temperature': row[14],
        }
        for row in rows
    ]

    return render(request, 'graphbof/GraphBofStatus.html', {'graph_bof_data': graph_bof_data})

# API view to return data as JSON
@api_view(['GET'])
def graph_bof_api(request):
    graph_bof_objects = GraphBof.objects.all()
    serializer = GraphBofSerializer(graph_bof_objects, many=True)
    return Response(serializer.data)


# View to render GraphLrf HTML page using raw SQL and cursor
def graph_lrf_status_view(request):
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT sr_no, heart_id, start_date, stop_date, steel_grade,
                   total_power_consumption, process_time_min, first_temprature, final_temprature,
                   SiMn, HcFe_Si, HcFeMn, Lime, CPC, Sequence_no
            FROM Graph.dbo.GraphLrf
        """)
        rows = cursor.fetchall()

    graph_lrf_data = [
        {
            'sr_no': row[0],
            'heart_id': row[1],
            'start_date': row[2],
            'stop_date': row[3],
            'steel_grade': row[4],
            'total_power_consumption': row[5],
            'process_time_min': row[6],
            'first_temprature': row[7],
            'final_temprature': row[8],
            'SiMn': row[9],
            'HcFe_Si': row[10],
            'HcFeMn': row[11],
            'Lime': row[12],
            'CPC': row[13],
            'Sequence_no': row[14],
        }
        for row in rows
    ]

    return render(request, 'graphlrf/GraphLrfStatus.html', {'graph_lrf_data': graph_lrf_data})


# API view to return GraphLrf data as JSON
@api_view(['GET'])
def graph_lrf_api(request):
    graph_lrf_objects = GraphLrf.objects.all()
    serializer = GraphLrfSerializer(graph_lrf_objects, many=True)
    return Response(serializer.data)

