# Unified Backend Project

This is a consolidated Django project that combines three separate backend applications into a single unified system.

## Project Structure

```
unified_backend/
├── manage.py                    # Main Django management script
├── unified_project/             # Django project configuration
│   ├── settings.py             # Unified settings with multi-database support
│   ├── urls.py                 # Main URL configuration
│   ├── wsgi.py                 # WSGI configuration
│   └── asgi.py                 # ASGI configuration
├── unified_app/                 # Single Django app containing all functionality
│   ├── models.py               # All models (CarInfo, Bof, SMSTracking)
│   ├── views.py                # All views from the three projects
│   ├── serializers.py          # All DRF serializers
│   ├── urls.py                 # All URL patterns
│   ├── admin.py                # Admin configurations
│   ├── routers.py              # Database routing logic
│   └── migrations/             # Database migrations
└── templates/                   # HTML templates
    ├── car/                    # Car-related templates
    ├── bof/                    # BOF-related templates
    └── sms/                    # SMS tracking templates
```

## Features Consolidated

### 1. Car Movement System
- **Model**: `CarInfo`
- **Database**: `car` (SQL Server)
- **URLs**: 
  - `/car/status/` - HTML view
  - `/api/car/` - JSON API
- **Template**: `car/CarStatus.html`

### 2. BOF (Basic Oxygen Furnace) System
- **Model**: `Bof`
- **Database**: `SmsBOF` (SQL Server)
- **URLs**: 
  - `/bof/main/` - HTML view
  - `/api/bof/` - JSON API
  - `/main/` - Backward compatibility redirect
- **Template**: `bof/BofStatus.html`

### 3. SMS Tracking System
- **Model**: `SMSTracking`
- **Database**: `SMSDB` (SQL Server)
- **URLs**: 
  - `/sms/home/<USER>
  - `/api/sms/` - JSON API
  - `/home/<USER>
- **Template**: `sms/home.html`

## Database Configuration

The project uses multiple SQL Server databases with automatic routing:

- **default**: `car` database for CarInfo model
- **bof_db**: `SmsBOF` database for Bof model  
- **sms_db**: `SMSDB` database for SMSTracking model

Database routing is handled automatically by `unified_app.routers.DatabaseRouter`.

## API Endpoints

All original API endpoints are preserved:

- `GET /api/car/` - Car information
- `GET /api/bof/` - BOF status data
- `GET /api/sms/` - SMS tracking data

## Running the Project

1. Ensure all dependencies are installed (Django, djangorestframework, corsheaders, mssql)
2. Configure database connections in `settings.py`
3. Run migrations: `python manage.py migrate`
4. Start the server: `python manage.py runserver`

## Admin Interface

Access the admin interface at `/admin/` to manage all three systems:
- Car Information
- BOF Status
- SMS Tracking

## Backward Compatibility

The unified project maintains backward compatibility with original URL patterns:
- `/main/` redirects to BOF status
- `/home/<USER>

## Benefits of Consolidation

1. **Single Management**: One `manage.py` file for all operations
2. **Unified Admin**: Single admin interface for all systems
3. **Shared Dependencies**: Common Django/DRF configuration
4. **Simplified Deployment**: One project to deploy instead of three
5. **Consistent Structure**: Standardized code organization
6. **Multi-Database Support**: Proper database routing for different systems
