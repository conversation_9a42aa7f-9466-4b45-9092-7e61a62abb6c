from django.db import models


class CarInfo(models.Model):
    """Model for Car Information - uses 'default' database (car)"""
    CAR_POSITION_CHOICES = [
        ('Tapping', 'Tapping'),
        ('Lifting', 'Lifting'),
        ('Intermediate', 'Intermediate'),
    ]
    sr_no = models.AutoField(primary_key=True, db_column='Sr_no')
    car_description = models.CharField(max_length=255, db_column='Car_Description')
    area = models.CharField(max_length=100, db_column='Area')
    unit = models.CharField(max_length=100, db_column='Unit')
    car_position = models.CharField(max_length=20, choices=CAR_POSITION_CHOICES, db_column='Car_Position')

    def __str__(self):
        return f"{self.car_description} - {self.car_position}"

    class Meta:
        db_table = 'car_info'
        managed = False


class Bof(models.Model):
    """Model for BOF Information - uses 'bof_db' database (SmsBOF)"""
    sr_no = models.IntegerField(primary_key=True, db_column='Sr.no')
    area = models.CharField(max_length=100, db_column='Area')         
    units = models.CharField(max_length=100, db_column='Units')        
    status = models.CharField(max_length=50, db_column='Status')        
    heat_id = models.CharField(max_length=100, db_column='HeatId')      
    start_date = models.DateTimeField(null=True, blank=True, db_column='StartDate')
    stop_date = models.DateTimeField(null=True, blank=True, db_column='StopDate')
    steel_grade = models.CharField(max_length=100, db_column='SteelGrade')  

    def __str__(self):
        return f"{self.area} - {self.units} - {self.status}"

    class Meta:
        db_table = 'BOF'
        managed = False


class SMSTracking(models.Model):
    """Model for SMS Tracking Information - uses 'sms_db' database (SMSDB)"""
    SR_No = models.IntegerField(primary_key=True)
    STATION_CODE = models.IntegerField()
    AREA_CODE = models.IntegerField()
    UNIT_CODE = models.IntegerField()
    AREA_DESCRIPTION = models.CharField(max_length=255)
    CURRENT_STATUS = models.IntegerField()

    class Meta:
        db_table = 'SMS_TRACKING'  
        managed = False  # table already exists in SQL Server

    @property
    def color(self):
        return "Green" if self.CURRENT_STATUS == 1 else "Grey"

    def __str__(self):
        return f"{self.AREA_DESCRIPTION} - Status: {self.CURRENT_STATUS}"

class HeatProcess(models.Model):
    sr_no = models.AutoField(primary_key=True, db_column='sr_no')
    heat_id = models.CharField(max_length=10, db_column='heat_id')
    unit = models.CharField(max_length=20, db_column='unit')
    unit_no = models.CharField(max_length=20, db_column='unit_no')
    laddle_arrival_time = models.DateTimeField(null=True, blank=True, db_column='laddle_arrival_time')
    start_time = models.DateTimeField(null=True, blank=True, db_column='start_time')
    stop_time = models.DateTimeField(null=True, blank=True, db_column='stop_time')
    current_status = models.CharField(max_length=50, db_column='current_status')

    def __str__(self):
        return f"{self.heat_id} - {self.unit}"

    class Meta:
        db_table = 'unit_tracking'
        managed = False

class GraphBof(models.Model):
    sr_no = models.AutoField(primary_key=True, db_column='Sr_no')
    heat_id = models.CharField(max_length=50, db_column='Heat_id')
    start_date = models.DateTimeField(db_column='Start_date')
    stop_date = models.DateTimeField(db_column='Stop_date')
    steel_grade = models.CharField(max_length=50, db_column='Steel_grade')
    hot_metal_temperature = models.CharField(max_length=50, db_column='Hot_metal_temperature')
    hot_metal_weight = models.CharField(max_length=50, db_column='Hot_metal_weight')
    total_scrap_charge = models.CharField(max_length=50, db_column='Total_scrap_charge')
    total_oxygen_consumption = models.CharField(max_length=50, db_column='Total_oxygen_consumption')
    tap_to_tap_time = models.CharField(max_length=50, db_column='Tap_to_tap_time')
    blowing_duration = models.CharField(max_length=50, db_column='Blowing_duration')
    aim_carbon = models.CharField(max_length=50, db_column='Aim_carbon')
    actual_carbon = models.CharField(max_length=50, db_column='Actual_carbon')
    aim_temperature = models.CharField(max_length=50, db_column='Aim_temperature')
    actual_temperature = models.CharField(max_length=50, db_column='Actual_temperature')

    class Meta:
        db_table = 'GraphBof'

class GraphLrf(models.Model):
    sr_no = models.AutoField(primary_key=True, db_column='sr_no')
    heart_id = models.CharField(max_length=50, db_column='heart_id')
    start_date = models.CharField(max_length=50, db_column='start_date')
    stop_date = models.CharField(max_length=50, db_column='stop_date')
    steel_grade = models.CharField(max_length=50, db_column='steel_grade')
    total_power_consumption = models.CharField(max_length=50, db_column='total_power_consumption')
    process_time_min = models.IntegerField(db_column='process_time_min')
    first_temprature = models.CharField(max_length=50, db_column='first_temprature')
    final_temprature = models.CharField(max_length=50, db_column='final_temprature')
    si_mn = models.CharField(max_length=50, db_column='SiMn')

    class Meta:
        db_table = 'GraphLrf'

