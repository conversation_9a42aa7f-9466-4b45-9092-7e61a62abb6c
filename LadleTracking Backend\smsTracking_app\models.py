from django.db import models


class CarInfo(models.Model):
    """Model for Car Information - uses 'default' database (car)"""
    CAR_POSITION_CHOICES = [
        ('Tapping', 'Tapping'),
        ('Lifting', 'Lifting'),
        ('Intermediate', 'Intermediate'),
    ]
    sr_no = models.AutoField(primary_key=True, db_column='Sr_no')
    car_description = models.CharField(max_length=255, db_column='Car_Description')
    area = models.CharField(max_length=100, db_column='Area')
    unit = models.CharField(max_length=100, db_column='Unit')
    car_position = models.CharField(max_length=20, choices=CAR_POSITION_CHOICES, db_column='Car_Position')

    def __str__(self):
        return f"{self.car_description} - {self.car_position}"

    class Meta:
        db_table = 'car_info'
        managed = False


class Bof(models.Model):
    """Model for BOF Information - uses 'bof_db' database (SmsBOF)"""
    sr_no = models.IntegerField(primary_key=True, db_column='Sr.no')
    area = models.CharField(max_length=100, db_column='Area')         
    units = models.CharField(max_length=100, db_column='Units')        
    status = models.CharField(max_length=50, db_column='Status')        
    heat_id = models.CharField(max_length=100, db_column='HeatId')      
    start_date = models.DateTimeField(null=True, blank=True, db_column='StartDate')
    stop_date = models.DateTimeField(null=True, blank=True, db_column='StopDate')
    steel_grade = models.CharField(max_length=100, db_column='SteelGrade')  

    def __str__(self):
        return f"{self.area} - {self.units} - {self.status}"

    class Meta:
        db_table = 'BOF'
        managed = False


class SMSTracking(models.Model):
    """Model for SMS Tracking Information - uses 'sms_db' database (SMSDB)"""
    SR_No = models.IntegerField(primary_key=True)
    STATION_CODE = models.IntegerField()
    AREA_CODE = models.IntegerField()
    UNIT_CODE = models.IntegerField()
    AREA_DESCRIPTION = models.CharField(max_length=255)
    CURRENT_STATUS = models.IntegerField()

    class Meta:
        db_table = 'SMS_TRACKING'  
        managed = False  # table already exists in SQL Server

    @property
    def color(self):
        return "Green" if self.CURRENT_STATUS == 1 else "Grey"

    def __str__(self):
        return f"{self.AREA_DESCRIPTION} - Status: {self.CURRENT_STATUS}"
