# Generated by Django 5.0.13 on 2025-06-12 04:13

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='HeatProcess',
            fields=[
                ('sr_no', models.AutoField(primary_key=True, serialize=False)),
                ('heat_id', models.CharField(max_length=10)),
                ('unit', models.Char<PERSON>ield(max_length=20)),
                ('unit_no', models.Char<PERSON>ield(max_length=20)),
                ('ladle_arrival_time', models.DateTimeField(blank=True, null=True)),
                ('start_time', models.DateTimeField(blank=True, null=True)),
                ('stop_time', models.DateTimeField(blank=True, null=True)),
                ('current_status', models.Char<PERSON>ield(max_length=50)),
            ],
        ),
    ]
