from django.shortcuts import render
from django.db import connection
from rest_framework.decorators import api_view
from rest_framework.response import Response
from .models import CarInfo, Bof, SMSTracking
from .serializers import CarInfoSerializer, BofSerializer, SMSTrackingSerializer


# ============ CAR VIEWS ============

def car_status_view(request):
    """View to render HTML page for car status using raw SQL and cursor"""
    with connection.cursor() as cursor:
        cursor.execute("SELECT Sr_no, Car_Description, Area, Unit, Car_Position FROM car_info")
        rows = cursor.fetchall()

    cars = [
        {
            'Sr_no': row[0],
            'Car_Description': row[1],
            'Area': row[2],
            'Unit': row[3],
            'Car_Position': row[4],
        }
        for row in rows
    ]

    return render(request, 'car/CarStatus.html', {'cars': cars})


@api_view(['GET'])
def car_api(request):
    """API view to return car data as JSON"""
    car_objects = CarInfo.objects.using('default').all()
    serializer = CarInfoSerializer(car_objects, many=True)
    return Response(serializer.data)


# ============ BOF VIEWS ============

def bof_status(request):
    """View to render HTML page for BOF status using raw SQL and cursor"""
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT [Sr.no], [Area], [Units], [Status], [HeatId], 
                   [StartDate], [StopDate], [SteelGrade] 
            FROM dbo.BOF
        """)
        rows = cursor.fetchall()
        columns = ['Sr_no', 'Area', 'Units', 'Status', 'HeatId', 'StartDate', 'StopDate', 'SteelGrade']
        data = [dict(zip(columns, row)) for row in rows]
    return render(request, 'bof/BofStatus.html', {'data': data})


@api_view(['GET'])
def bof_api(request):
    """API view to return BOF data as JSON"""
    bof_objects = Bof.objects.using('bof_db').all()
    serializer = BofSerializer(bof_objects, many=True)
    return Response(serializer.data)


# ============ SMS TRACKING VIEWS ============

def sms_home(request):
    """View to render HTML page for SMS tracking using raw SQL and cursor"""
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT 
                SR_No, STATION_CODE, AREA_CODE, UNIT_CODE,
                AREA_DESCRIPTION, CURRENT_STATUS
            FROM SMS_TRACKING
        """)
        rows = cursor.fetchall()

        # Convert rows to list of dicts
        data = []
        for row in rows:
            color = "Green" if row[5] == 1 else "Grey"
            data.append({
                'SR_No': row[0],
                'STATION_CODE': row[1],
                'AREA_CODE': row[2],
                'UNIT_CODE': row[3],
                'AREA_DESCRIPTION': row[4],
                'CURRENT_STATUS': row[5],
                'COLOR': color  # Uppercase so it matches the HTML template
            })

    return render(request, 'sms/home.html', {'data': data})


@api_view(['GET'])
def smsdb_api(request):
    """API view to return SMS tracking data as JSON"""
    sms_objects = SMSTracking.objects.using('sms_db').all()
    serializer = SMSTrackingSerializer(sms_objects, many=True)
    return Response(serializer.data)
