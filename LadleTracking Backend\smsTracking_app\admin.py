from django.contrib import admin
from .models import CarInfo, Bof, SMSTracking


@admin.register(CarInfo)
class CarInfoAdmin(admin.ModelAdmin):
    list_display = ('sr_no', 'car_description', 'area', 'unit', 'car_position')
    list_filter = ('car_position', 'area', 'unit')
    search_fields = ('car_description', 'area', 'unit')


@admin.register(Bof)
class BofAdmin(admin.ModelAdmin):
    list_display = ('sr_no', 'area', 'units', 'status', 'heat_id', 'start_date', 'stop_date', 'steel_grade')
    list_filter = ('status', 'area', 'steel_grade')
    search_fields = ('heat_id', 'area', 'units', 'steel_grade')
    date_hierarchy = 'start_date'


@admin.register(SMSTracking)
class SMSTrackingAdmin(admin.ModelAdmin):
    list_display = (
        'SR_No',
        'STATION_CODE',
        'AREA_CODE',
        'UNIT_CODE',
        'AREA_DESCRIPTION',
        'CURRENT_STATUS',
        'color_display',
    )
    list_filter = ('CURRENT_STATUS', 'AREA_CODE', 'STATION_CODE')
    search_fields = ('AREA_DESCRIPTION', 'SR_No')

    def color_display(self, obj):
        return obj.color
    color_display.short_description = 'Color'
