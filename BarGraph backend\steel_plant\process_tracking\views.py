from django.shortcuts import render
from django.db import connection
from rest_framework.views import APIView
from rest_framework.response import Response

# Homepage view
def home(request):
    return render(request, 'process_tracking/home.html')


# View to render heat process table from raw SQL
def heat_process_table(request):
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT [sr_no], [heat_id], [unit], [unit_no], 
                   [laddle_arrival_time], [start_time], 
                   [stop_time], [current_status]
            FROM [BOFDB].[dbo].[unit_tracking]
            ORDER BY [sr_no]
        """)
        rows = cursor.fetchall()

    processes = [
        {
            'sr_no': row[0],
            'heat_id': row[1],
            'unit': row[2],
            'unit_no': row[3],
            'laddle_arrival_time': row[4],  
            'start_time': row[5],
            'stop_time': row[6],
            'current_status': row[7],
        }
        for row in rows
    ]

    return render(request, 'process_tracking/heat_process_table.html', {'processes': processes})

#api
class HeatProcessRawAPIView(APIView):
    def get(self, request):
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT [sr_no], [heat_id], [unit], [unit_no], 
                       [laddle_arrival_time], [start_time], 
                       [stop_time], [current_status]
                FROM [BOFDB].[dbo].[unit_tracking]
                ORDER BY [sr_no]
            """)
            rows = cursor.fetchall()

        data = [
            {
                'sr_no': row[0],
                'heat_id': row[1],
                'unit': row[2],
                'unit_no': row[3],
                'laddle_arrival_time': row[4],  
                'start_time': row[5],
                'stop_time': row[6],
                'current_status': row[7],
            }
            for row in rows
        ]
        return Response(data)
