class DatabaseRouter:
    """
    A router to control all database operations on models
    """

    route_app_labels = {'smsTracking_app'}

    def db_for_read(self, model, **hints):
        """Suggest the database to read from."""
        if model._meta.app_label == 'smsTracking_app':
            if model.__name__ == 'CarInfo':
                return 'default'  # car database
            elif model.__name__ == 'Bof':
                return 'bof_db'   # SmsBOF database
            elif model.__name__ == 'SMSTracking':
                return 'sms_db'   # SMSDB database
        return None

    def db_for_write(self, model, **hints):
        """Suggest the database to write to."""
        if model._meta.app_label == 'smsTracking_app':
            if model.__name__ == 'CarInfo':
                return 'default'  # car database
            elif model.__name__ == 'Bof':
                return 'bof_db'   # SmsBOF database
            elif model.__name__ == 'SMSTracking':
                return 'sms_db'   # SMSDB database
        return None

    def allow_relation(self, obj1, obj2, **hints):
        """Allow relations if models are in the same app."""
        db_set = {'default', 'bof_db', 'sms_db'}
        if obj1._state.db in db_set and obj2._state.db in db_set:
            return True
        return None

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """Ensure that certain apps' models get created on the right database."""
        if app_label == 'smsTracking_app':
            if model_name == 'carinfo':
                return db == 'default'
            elif model_name == 'bof':
                return db == 'bof_db'
            elif model_name == 'smstracking':
                return db == 'sms_db'
        return None
