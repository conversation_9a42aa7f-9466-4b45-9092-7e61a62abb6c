from rest_framework import serializers
from .models import CarInfo, Bof, SMSTracking, HeatProcess, GraphBof, GraphLrf


class CarInfoSerializer(serializers.ModelSerializer):
    """Serializer for CarInfo model"""
    class Meta:
        model = CarInfo
        fields = '__all__'


class BofSerializer(serializers.ModelSerializer):
    """Serializer for Bof model"""
    class Meta:
        model = Bof
        fields = '__all__'


class SMSTrackingSerializer(serializers.ModelSerializer):
    """Serializer for SMSTracking model"""
    color = serializers.ReadOnlyField()

    class Meta:
        model = SMSTracking
        fields = ['SR_No', 'STATION_CODE', 'AREA_CODE', 'UNIT_CODE', 'AREA_DESCRIPTION', 'CURRENT_STATUS', 'color']


class HeatProcessSerializer(serializers.ModelSerializer):
    class Meta:
        model = HeatProcess
        fields = '__all__'


class GraphBofSerializer(serializers.ModelSerializer):
    class Meta:
        model = GraphBof
        fields = '__all__'


class GraphLrfSerializer(serializers.ModelSerializer):
    class Meta:
        model = GraphLrf
        fields = '__all__'

