from rest_framework import serializers
from .models import CarInfo, Bof, SMSTracking


class CarInfoSerializer(serializers.ModelSerializer):
    """Serializer for CarInfo model"""
    class Meta:
        model = CarInfo
        fields = '__all__'


class BofSerializer(serializers.ModelSerializer):
    """Serializer for Bof model"""
    class Meta:
        model = Bof
        fields = '__all__'


class SMSTrackingSerializer(serializers.ModelSerializer):
    """Serializer for SMSTracking model"""
    color = serializers.ReadOnlyField()

    class Meta:
        model = SMSTracking
        fields = ['SR_No', 'STATION_CODE', 'AREA_CODE', 'UNIT_CODE', 'AREA_DESCRIPTION', 'CURRENT_STATUS', 'color']
