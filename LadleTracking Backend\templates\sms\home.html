<!DOCTYPE html>
<html>
<head>
    <title>SMS Tracking Data</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        h1 {
            text-align: center;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 25px;
        }

        th, td {
            border: 1px solid #ccc;
            padding: 10px;
            text-align: center;
        }

        th {
            background-color: #f2f2f2;
        }

        .Green {
            background-color: #00b300; /* Bright green */
            color: white;
            font-weight: bold;
        }

        .Grey {
            background-color: #5a5a5a; /* Dark grey */
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>SMS Tracking Data</h1>
    <table>
        <thead>
            <tr>
                <th>SR_No</th>
                <th>STATION_CODE</th>
                <th>AREA_CODE</th>
                <th>UNIT_CODE</th>
                <th>AREA_DESCRIPTION</th>
                <th>CURRENT_STATUS</th>
                <th>COLOR</th>
            </tr>
        </thead>
        <tbody>
            {% for row in data %}
            <tr>
                <td>{{ row.SR_No }}</td>
                <td>{{ row.STATION_CODE }}</td>
                <td>{{ row.AREA_CODE }}</td>
                <td>{{ row.UNIT_CODE }}</td>
                <td>{{ row.AREA_DESCRIPTION }}</td>
                <td>{{ row.CURRENT_STATUS }}</td>
                <td class="{{ row.COLOR }}">{{ row.COLOR }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
